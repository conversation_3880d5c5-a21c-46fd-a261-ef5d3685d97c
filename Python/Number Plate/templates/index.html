<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License Plate Recognition System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .mode-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .mode-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .status-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .status-value {
            color: #7f8c8d;
        }

        .results-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .image-display {
            text-align: center;
        }

        .image-display h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .image-container {
            border: 3px dashed #bdc3c7;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .image-container img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .placeholder-text {
            color: #7f8c8d;
            font-style: italic;
        }

        .ai-results {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #27ae60;
        }

        .ai-results h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .result-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }

        .result-item:last-child {
            margin-bottom: 0;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .mode-indicator {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .mode-web {
            background: #3498db;
            color: white;
        }

        .mode-auto {
            background: #27ae60;
            color: white;
        }

        @media (max-width: 768px) {
            .control-panel,
            .results-section {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 License Plate Recognition</h1>
            <p>AI-Powered Vehicle Monitoring System</p>
        </div>

        <div class="main-content">
            <!-- Alert Messages -->
            <div id="alertContainer"></div>

            <!-- Status Panel -->
            <div class="status-panel">
                <div class="status-item">
                    <span class="status-label">Current Mode:</span>
                    <span class="status-value">
                        <span id="currentMode" class="mode-indicator mode-web">Web</span>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">System Status:</span>
                    <span id="systemStatus" class="status-value">Ready</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Last Update:</span>
                    <span id="lastUpdate" class="status-value">Never</span>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <!-- Web Mode Controls -->
                <div class="mode-section">
                    <h3>📸 Web Mode (Manual)</h3>
                    <button id="captureBtn" class="btn">
                        Capture & Analyze Now
                    </button>
                    <p style="color: #7f8c8d; font-size: 0.9em; margin-top: 10px;">
                        Click to manually capture and process an image from the camera.
                    </p>
                </div>

                <!-- Auto Mode Controls -->
                <div class="mode-section">
                    <h3>⏰ Auto Mode (Scheduled)</h3>
                    <div class="input-group">
                        <label for="intervalInput">Capture Interval (seconds):</label>
                        <input type="number" id="intervalInput" min="1" max="3600" value="60" placeholder="Enter seconds">
                    </div>
                    <button id="startAutoBtn" class="btn btn-success">
                        Start Auto Mode
                    </button>
                    <button id="stopAutoBtn" class="btn btn-danger" style="display: none;">
                        Stop Auto Mode
                    </button>
                    <p style="color: #7f8c8d; font-size: 0.9em; margin-top: 10px;">
                        Automatically capture images at the specified interval.
                    </p>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <p>Processing image...</p>
            </div>

            <!-- Results Section -->
            <div class="results-section">
                <!-- Image Display -->
                <div class="image-display">
                    <h3>📷 Latest Capture</h3>
                    <div class="image-container" id="imageContainer">
                        <p class="placeholder-text">No image captured yet</p>
                    </div>
                </div>

                <!-- AI Results -->
                <div class="ai-results">
                    <h3>🤖 AI Analysis Results</h3>
                    <div id="aiResults">
                        <p class="placeholder-text">No analysis results yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentMode = 'web';
        let autoModeInterval = null;
        let statusUpdateInterval = null;

        // DOM elements
        const captureBtn = document.getElementById('captureBtn');
        const startAutoBtn = document.getElementById('startAutoBtn');
        const stopAutoBtn = document.getElementById('stopAutoBtn');
        const intervalInput = document.getElementById('intervalInput');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const alertContainer = document.getElementById('alertContainer');
        const imageContainer = document.getElementById('imageContainer');
        const aiResults = document.getElementById('aiResults');
        const currentModeSpan = document.getElementById('currentMode');
        const systemStatus = document.getElementById('systemStatus');
        const lastUpdate = document.getElementById('lastUpdate');

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateStatus();
            startStatusPolling();
        });

        function setupEventListeners() {
            captureBtn.addEventListener('click', manualCapture);
            startAutoBtn.addEventListener('click', startAutoMode);
            stopAutoBtn.addEventListener('click', stopAutoMode);
        }

        function startStatusPolling() {
            // Update status every 2 seconds
            statusUpdateInterval = setInterval(updateStatus, 2000);
        }

        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                currentMode = status.mode;
                updateModeDisplay(status.mode);
                updateSystemStatus(status.status);
                updateLastUpdate();
                
                // Update UI based on mode
                if (status.mode === 'auto' && status.auto_mode_running) {
                    showAutoModeActive();
                } else {
                    showWebModeActive();
                }
                
                // Update results if there's a recent capture
                if (status.last_capture) {
                    updateResults(status.last_capture);
                }
                
            } catch (error) {
                console.error('Failed to update status:', error);
            }
        }

        function updateModeDisplay(mode) {
            currentModeSpan.textContent = mode.charAt(0).toUpperCase() + mode.slice(1);
            currentModeSpan.className = `mode-indicator mode-${mode}`;
        }

        function updateSystemStatus(status) {
            systemStatus.textContent = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
        }

        function updateLastUpdate() {
            lastUpdate.textContent = new Date().toLocaleTimeString();
        }

        function showAutoModeActive() {
            startAutoBtn.style.display = 'none';
            stopAutoBtn.style.display = 'block';
            captureBtn.disabled = true;
            intervalInput.disabled = true;
        }

        function showWebModeActive() {
            startAutoBtn.style.display = 'block';
            stopAutoBtn.style.display = 'none';
            captureBtn.disabled = false;
            intervalInput.disabled = false;
        }

        async function manualCapture() {
            showLoading(true);
            captureBtn.disabled = true;
            
            try {
                const response = await fetch('/api/capture', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Image captured and processed successfully!', 'success');
                    updateResults(result);
                    loadLatestImage();
                } else {
                    showAlert(`Capture failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showAlert(`Network error: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                captureBtn.disabled = false;
            }
        }

        async function startAutoMode() {
            const interval = parseInt(intervalInput.value);
            
            if (!interval || interval < 1) {
                showAlert('Please enter a valid interval (minimum 1 second)', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/start_auto', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        interval_seconds: interval
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`Auto mode started with ${interval} second interval`, 'success');
                    showAutoModeActive();
                } else {
                    showAlert(`Failed to start auto mode: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showAlert(`Network error: ${error.message}`, 'error');
            }
        }

        async function stopAutoMode() {
            try {
                const response = await fetch('/api/stop_auto', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Auto mode stopped, switched to web mode', 'success');
                    showWebModeActive();
                } else {
                    showAlert(`Failed to stop auto mode: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showAlert(`Network error: ${error.message}`, 'error');
            }
        }

        function loadLatestImage() {
            const img = document.createElement('img');
            img.onload = function() {
                imageContainer.innerHTML = '';
                imageContainer.appendChild(img);
            };
            img.onerror = function() {
                imageContainer.innerHTML = '<p class="placeholder-text">Failed to load image</p>';
            };
            img.src = `/api/latest_image?t=${Date.now()}`;
        }

        function updateResults(captureResult) {
            if (captureResult && captureResult.success) {
                loadLatestImage();
                
                // Update AI results (placeholder for now)
                aiResults.innerHTML = `
                    <div class="result-item">
                        <strong>Capture Time:</strong> ${new Date(captureResult.timestamp).toLocaleString()}
                    </div>
                    <div class="result-item">
                        <strong>Mode:</strong> ${captureResult.mode}
                    </div>
                    <div class="result-item">
                        <strong>Status:</strong> Processing completed successfully
                    </div>
                `;
            }
        }

        function showLoading(show) {
            loadingIndicator.style.display = show ? 'block' : 'none';
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            alertContainer.appendChild(alertDiv);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5001);
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (statusUpdateInterval) {
                clearInterval(statusUpdateInterval);
            }
        });
    </script>
</body>
</html>
