[2025-09-05 21:32:41] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:32:41] INFO     Gemini AI client initialized successfully
[2025-09-05 21:32:41] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:32:43] ERROR    Gemini API connection test failed: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:32:43] WARNING  AI processor test failed. License plate detection may not work.
[2025-09-05 21:32:43] INFO     📷 Testing camera connection...
[2025-09-05 21:32:43] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:32:43] ERROR    RTSP URL not configured
[2025-09-05 21:32:43] WARNING  ❌ Real camera connection failed after all attempts
[2025-09-05 21:32:43] INFO     🎭 Switching to mock camera for demonstration
[2025-09-05 21:32:43] INFO     Connecting to mock camera...
[2025-09-05 21:32:44] INFO     Mock camera connected successfully
[2025-09-05 21:32:44] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:32:58] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 21:32:58] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:58] INFO     Disconnecting from mock camera
[2025-09-05 21:32:58] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:32:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:41:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:41:57] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:41:57] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:41:57] INFO     🎭 Using mock camera for testing
[2025-09-05 21:41:57] INFO     Gemini AI client initialized successfully
[2025-09-05 21:41:57] INFO     Connecting to mock camera...
[2025-09-05 21:41:58] INFO     Mock camera connected successfully
[2025-09-05 21:42:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:23] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:42:23] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:42:23] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:23] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:23] INFO     Connecting to mock camera...
[2025-09-05 21:42:24] INFO     Mock camera connected successfully
[2025-09-05 21:42:24] INFO     Generated mock image without license plate
[2025-09-05 21:42:24] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:24] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:26] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Empty road with white dashed lines under a light blue sky.', 'word_count': 11, 'raw_response': 'Empty road with white dashed lines under a light blue sky.', 'image_path': 'captured_images/mock_capture_20250905_214224.jpg'}
[2025-09-05 21:42:33] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:34] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:42:34] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:42:34] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:34] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:34] INFO     Connecting to mock camera...
[2025-09-05 21:42:35] INFO     Mock camera connected successfully
[2025-09-05 21:42:35] INFO     Generated mock image with license plate: MNO678
[2025-09-05 21:42:35] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:35] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:37] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: MNO678\n- Location: On the rear of a blue rectangular object (simulating a vehicle) on the road.\n- Confidence: High\n- Details: The license plate is white text on a dark blue background within a blue rectangular frame.', 'image_path': 'captured_images/mock_capture_20250905_214235.jpg'}
[2025-09-05 21:42:37] INFO     License plates detected in captured_images/mock_capture_20250905_214235.jpg: [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}]
[2025-09-05 21:43:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:43:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:43:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:43:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:43:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:43:29] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:29] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:43:29] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:43:31] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:31] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:43:31] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:43:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:43:35] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:44:09] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:09] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:09] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:09] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:09] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:09] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:44:13] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:13] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:13] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Image captured successfully: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Processing image for Object Detection: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:14] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person looking down near large illuminated number three sculpture.', 'word_count': 9, 'raw_response': 'Person looking down near large illuminated number three sculpture.', 'image_path': 'captured_images/capture_20250905_214413.jpg'}
[2025-09-05 21:44:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:29] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:44:30] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 21:44:30] INFO     📷 Testing camera connection...
[2025-09-05 21:44:30] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:44:30] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:44:33] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:33] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:33] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 21:44:33] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:44:38] INFO     Received signal 15, shutting down gracefully...
[2025-09-05 21:44:38] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:38] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:38] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:44:39] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:39] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:39] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:45:40] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:45:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:45:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:45:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:45:40] INFO     Gemini AI client initialized successfully
[2025-09-05 21:45:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:45:43] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:45:43] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:45:43] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Image captured successfully: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Processing image for Object Detection: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] ERROR    Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:45:43] ERROR    API call failed: Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:35] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:47:36] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:47:36] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:47:36] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:47:36] INFO     Gemini AI client initialized successfully
[2025-09-05 21:47:36] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:47:39] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:47:39] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:47:39] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Image captured successfully: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Processing image for Object Detection: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:41] ERROR    Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:41] ERROR    API call failed: Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:55:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:55:24] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:55:24] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:55:24] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:55:25] INFO     Gemini AI client initialized successfully
[2025-09-05 21:55:25] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:28] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:55:28] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:55:28] INFO     ✅ Image captured and saved: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] INFO     Image captured successfully: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] INFO     Processing image for Object Detection: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] ERROR    Error processing image captured_images/capture_20250905_215528.jpg: Part.from_text() takes 1 positional argument but 2 were given
[2025-09-05 21:55:28] ERROR    API call failed: Error processing image captured_images/capture_20250905_215528.jpg: Part.from_text() takes 1 positional argument but 2 were given
[2025-09-05 21:55:47] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:55:48] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:55:48] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:55:48] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:55:48] INFO     Gemini AI client initialized successfully
[2025-09-05 21:55:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:55:48] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:55:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:50] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:55:50] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:55:54] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:55] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:55:55] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:56:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:56:03] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:56:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:56:03] INFO     🎭 Using mock camera for testing
[2025-09-05 21:56:03] INFO     Gemini AI client initialized successfully
[2025-09-05 21:56:03] INFO     Connecting to mock camera...
[2025-09-05 21:56:04] INFO     Mock camera connected successfully
[2025-09-05 21:56:04] INFO     Generated mock image with license plate: YZA890
[2025-09-05 21:56:04] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_215604.jpg
[2025-09-05 21:56:04] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_215604.jpg
[2025-09-05 21:56:06] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Blue vehicle with license plate YZA890 on gray road with dashed white lines.', 'word_count': 13, 'raw_response': 'Blue vehicle with license plate YZA890 on gray road with dashed white lines.', 'image_path': 'captured_images/mock_capture_20250905_215604.jpg'}
[2025-09-05 21:56:13] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:56:14] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:56:14] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:56:14] INFO     🎭 Using mock camera for testing
[2025-09-05 21:56:14] INFO     Gemini AI client initialized successfully
[2025-09-05 21:56:14] INFO     Connecting to mock camera...
[2025-09-05 21:56:15] INFO     Mock camera connected successfully
[2025-09-05 21:56:15] INFO     Generated mock image with license plate: VWX567
[2025-09-05 21:56:15] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_215615.jpg
[2025-09-05 21:56:15] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_215615.jpg
[2025-09-05 21:56:17] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'VWX567', 'location': 'Appears to be on the front of a blue vehicle', 'confidence': 'High', 'details': 'The license plate is in blue and white with the text "VWX567" visible.'}], 'plates_detected': 1, 'raw_response': '```\nLICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: VWX567\n- Location: Appears to be on the front of a blue vehicle\n- Confidence: High\n- Details: The license plate is in blue and white with the text "VWX567" visible.\n```', 'image_path': 'captured_images/mock_capture_20250905_215615.jpg'}
[2025-09-05 21:56:17] INFO     License plates detected in captured_images/mock_capture_20250905_215615.jpg: [{'plate_number': 'VWX567', 'location': 'Appears to be on the front of a blue vehicle', 'confidence': 'High', 'details': 'The license plate is in blue and white with the text "VWX567" visible.'}]
[2025-09-05 21:57:43] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:57:45] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:57:45] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:57:45] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:57:45] INFO     Gemini AI client initialized successfully
[2025-09-05 21:57:45] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:45] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:57:45] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:57:47] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:57:48] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:57:52] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:52] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:57:52] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:57:55] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:57:56] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:57:56] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:57:56] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:57:56] INFO     Gemini AI client initialized successfully
[2025-09-05 21:57:56] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:56] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:57:56] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:57:58] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:58] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:57:58] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:58:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:03] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:58:03] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:58:19] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:58:20] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:58:20] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:58:20] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:58:20] INFO     Gemini AI client initialized successfully
[2025-09-05 21:58:20] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:20] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:58:20] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:58:22] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:22] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:58:22] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:58:26] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:26] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:58:26] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:59:48] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:59:50] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:59:50] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:59:50] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:59:50] INFO     Gemini AI client initialized successfully
[2025-09-05 21:59:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:59:52] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:59:52] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:59:52] INFO     ✅ Image captured and saved: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] INFO     Image captured successfully: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] INFO     Processing image for Object Detection: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] ERROR    Error processing image captured_images/capture_20250905_215952.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 21:59:52] ERROR    API call failed: Error processing image captured_images/capture_20250905_215952.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:01:44] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:01:46] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:01:46] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:01:46] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:01:46] INFO     Gemini AI client initialized successfully
[2025-09-05 22:01:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:01:48] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:01:48] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:01:48] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:48] INFO     Image captured successfully: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:48] INFO     Processing image for Object Detection: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:50] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'A white lamp illuminates the corner with rounded architectural features, all in bright, minimalistic style.', 'word_count': 15, 'raw_response': 'A white lamp illuminates the corner with rounded architectural features, all in bright, minimalistic style.\n', 'image_path': 'captured_images/capture_20250905_220148.jpg'}
[2025-09-05 22:01:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:01:57] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:01:57] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:01:57] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:01:57] INFO     Gemini AI client initialized successfully
[2025-09-05 22:01:57] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:02:00] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:02:00] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:02:00] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:00] INFO     Image captured successfully: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:00] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:02] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_220200.jpg'}
[2025-09-05 22:02:02] INFO     No license plates detected in captured_images/capture_20250905_220200.jpg
[2025-09-05 22:04:01] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:04:02] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:04:02] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:04:02] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:04:02] INFO     Gemini AI client initialized successfully
[2025-09-05 22:04:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:04:04] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:04:04] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:04:04] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:04] INFO     Image captured successfully: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:04] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:06] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.\n', 'image_path': 'captured_images/capture_20250905_220404.jpg'}
[2025-09-05 22:04:06] INFO     No license plates detected in captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:39] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:04:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:04:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:04:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:04:40] INFO     Gemini AI client initialized successfully
[2025-09-05 22:04:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:04:42] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:04:42] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:04:42] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:42] INFO     Image captured successfully: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:42] INFO     Processing image for Object Detection: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:44] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': "Person's face is visible, possibly looking down, in a brightly lit indoor space.", 'word_count': 13, 'raw_response': "Person's face is visible, possibly looking down, in a brightly lit indoor space.\n", 'image_path': 'captured_images/capture_20250905_220442.jpg'}
[2025-09-05 22:06:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:06:03] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:06:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:06:03] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:06:03] INFO     Gemini AI client initialized successfully
[2025-09-05 22:06:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:06:06] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:06:06] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:06:06] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:06] INFO     Image captured successfully: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:06] INFO     Processing image for Object Detection: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:08] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Interior scene shows tile floor, kitchen area, and decorative light fixtures.', 'word_count': 11, 'raw_response': 'Interior scene shows tile floor, kitchen area, and decorative light fixtures.\n', 'image_path': 'captured_images/capture_20250905_220606.jpg'}
[2025-09-05 22:06:20] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:06:21] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:06:21] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:06:21] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:06:21] INFO     Gemini AI client initialized successfully
[2025-09-05 22:06:21] INFO     🤖 Testing AI processor connection...
[2025-09-05 22:06:22] INFO     Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:06:22] INFO     📷 Testing camera connection...
[2025-09-05 22:06:22] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 22:06:22] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:06:25] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:06:25] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:06:25] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 22:06:25] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 22:07:25] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     Image captured successfully: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     📷 Image captured: capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:07:25] INFO     Processing image for Object Detection: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:27] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Overturned plant and broken mirror inside, city buildings visible outside window.', 'word_count': 11, 'raw_response': 'Overturned plant and broken mirror inside, city buildings visible outside window.\n', 'image_path': 'captured_images/capture_20250905_220725.jpg'}
[2025-09-05 22:07:27] INFO     👁️  Scene: Overturned plant and broken mirror inside, city buildings visible outside window. (11 words)
[2025-09-05 22:07:27] INFO     SCENE: Overturned plant and broken mirror inside, city buildings visible outside window. | 11 words | capture_20250905_220725.jpg
[2025-09-05 22:08:27] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     Image captured successfully: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     📷 Image captured: capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:08:27] INFO     Processing image for Object Detection: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:30] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'View from window: City buildings at night, indoor plant and objects visible.', 'word_count': 12, 'raw_response': 'View from window: City buildings at night, indoor plant and objects visible.\n', 'image_path': 'captured_images/capture_20250905_220827.jpg'}
[2025-09-05 22:08:30] INFO     👁️  Scene: View from window: City buildings at night, indoor plant and objects visible. (12 words)
[2025-09-05 22:08:30] INFO     SCENE: View from window: City buildings at night, indoor plant and objects visible. | 12 words | capture_20250905_220827.jpg
[2025-09-05 22:09:30] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     Image captured successfully: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     📷 Image captured: capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:09:30] INFO     Processing image for Object Detection: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:32] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant sits inside, overlooking city buildings at night through window.', 'word_count': 11, 'raw_response': 'Potted plant sits inside, overlooking city buildings at night through window.\n', 'image_path': 'captured_images/capture_20250905_220930.jpg'}
[2025-09-05 22:09:32] INFO     👁️  Scene: Potted plant sits inside, overlooking city buildings at night through window. (11 words)
[2025-09-05 22:09:32] INFO     SCENE: Potted plant sits inside, overlooking city buildings at night through window. | 11 words | capture_20250905_220930.jpg
[2025-09-05 22:10:33] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     Image captured successfully: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     📷 Image captured: capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:10:33] INFO     Processing image for Object Detection: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:35] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Indoor scene with broken plant pot, looking out at city buildings at night.', 'word_count': 13, 'raw_response': 'Indoor scene with broken plant pot, looking out at city buildings at night.\n', 'image_path': 'captured_images/capture_20250905_221033.jpg'}
[2025-09-05 22:10:35] INFO     👁️  Scene: Indoor scene with broken plant pot, looking out at city buildings at night. (13 words)
[2025-09-05 22:10:35] INFO     SCENE: Indoor scene with broken plant pot, looking out at city buildings at night. | 13 words | capture_20250905_221033.jpg
[2025-09-05 22:11:06] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:11:08] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:11:08] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:11:08] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:11:08] INFO     Gemini AI client initialized successfully
[2025-09-05 22:11:08] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:11:10] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:11:10] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:11:10] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:10] INFO     Image captured successfully: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:10] INFO     Processing image for Object Detection: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:13] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Leather chair next to a window with wood floors and a wall air conditioner.', 'word_count': 14, 'raw_response': 'Leather chair next to a window with wood floors and a wall air conditioner.\n', 'image_path': 'captured_images/capture_20250905_221110.jpg'}
