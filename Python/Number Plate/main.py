"""
AI Vision Monitoring System - Main Application
Multi-mode system supporting License Plate Detection and Object Detection
"""
import time
import json
import os
import signal
import sys
import argparse
from datetime import datetime

# Import with fallback handling
try:
    import schedule
except ImportError:
    print("Error: 'schedule' library not installed. Run: pip install -r requirements.txt")
    sys.exit(1)

try:
    from dotenv import load_dotenv
except ImportError:
    print("Warning: 'python-dotenv' not installed. Environment variables won't be loaded from .env file")
    def load_dotenv():
        pass

# Camera handler imports with fallback to mock
try:
    from camera_handler import RTSPCameraHandler
    REAL_CAMERA_AVAILABLE = True
except ImportError:
    REAL_CAMERA_AVAILABLE = False
    print("Warning: Real camera handler not available. Using mock camera.")

from mock_camera import MockCameraHandler
from ai_processor import AIVisionProcessor
from logger import get_logger, get_license_plate_logger
from config_manager import get_config_manager


class AIVisionMonitor:
    def __init__(self, config_path="config.json", use_mock_camera=False):
        """Initialize the AI vision monitoring system"""
        # Load environment variables
        load_dotenv()

        self.config_manager = get_config_manager()
        self.config = self.config_manager.get_config()
        self.logger = get_logger()
        self.license_plate_logger = get_license_plate_logger()

        # Get current mode
        self.current_mode = self.config_manager.get_current_mode()
        self.mode_config = self.config_manager.get_mode_config()

        self.logger.info(f"🎯 Operating in {self.mode_config.get('name', self.current_mode)} mode")

        # Initialize camera handler (real or mock)
        self.use_mock_camera = use_mock_camera
        camera_config = self.config_manager.get_camera_config()

        if use_mock_camera or camera_config.get('mock', {}).get('fallback_on_failure', True):
            if use_mock_camera:
                self.camera_handler = MockCameraHandler(config_path)
                self.logger.info("🎭 Using mock camera for testing")
            elif not REAL_CAMERA_AVAILABLE:
                self.camera_handler = MockCameraHandler(config_path)
                self.logger.warning("📷 Real camera not available (OpenCV issue), using mock camera")
            else:
                try:
                    self.camera_handler = RTSPCameraHandler(config_path)
                    self.logger.info("📹 Initializing real RTSP camera")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize real camera: {e}")
                    self.logger.info("🎭 Falling back to mock camera")
                    self.camera_handler = MockCameraHandler(config_path)
                    self.use_mock_camera = True
        else:
            # Force real camera, no fallback
            self.camera_handler = RTSPCameraHandler(config_path)
            self.logger.info("📹 Initializing real RTSP camera (no fallback)")

        # Initialize AI processor
        self.ai_processor = AIVisionProcessor(config_path)

        self.running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, _frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()

    def capture_and_process(self):
        """Capture image from camera and process it with AI"""
        mode_name = self.mode_config.get('name', self.current_mode)
        print(f"\n📸 {datetime.now().strftime('%H:%M:%S')} - Capturing and processing ({mode_name})...")

        try:
            # Connect to camera if not already connected
            if not self.camera_handler.is_connected:
                self.logger.warning("Camera disconnected, attempting to reconnect...")
                if not self.camera_handler.connect():
                    self.logger.error("❌ Failed to reconnect to camera")
                    return

            # Capture image
            result = self.camera_handler.capture_image()

            if result:
                image_path, frame = result
                success = True
            else:
                success = False
                image_path = None

            if success and image_path:
                self.logger.info(f"📷 Image captured: {os.path.basename(image_path)}")

                # Process image with AI based on current mode
                self.logger.info(f"🤖 Processing with AI ({mode_name})...")
                ai_result = self.ai_processor.process_image(image_path, self.current_mode)

                if ai_result['success']:
                    if self.current_mode == "license_plate":
                        self._handle_license_plate_result(ai_result)
                    elif self.current_mode == "object_detection":
                        self._handle_object_detection_result(ai_result)
                    else:
                        self.logger.warning(f"Unknown mode: {self.current_mode}")
                else:
                    error_msg = ai_result.get('error', 'Unknown AI processing error')
                    self.logger.error(f"❌ AI processing failed: {error_msg}")

            else:
                self.logger.error("❌ Failed to capture image")

        except Exception as e:
            self.logger.error(f"❌ Error during capture/processing: {str(e)}")

    def _handle_license_plate_result(self, ai_result: dict):
        """Handle license plate detection results"""
        plates_detected = ai_result.get('plates_detected', 0)

        if plates_detected > 0:
            self.logger.info(f"🎯 Found {plates_detected} license plate(s)!")

            for i, plate in enumerate(ai_result['license_plates'], 1):
                plate_number = plate.get('plate_number', 'Unknown')
                location = plate.get('location', 'Unknown location')
                confidence = plate.get('confidence', 'Unknown')

                # Clean and format the detection info
                clean_location = location.replace('On a blue license plate, part of a vehicle', 'Vehicle').replace('On the rear of a blue rectangular object', 'Rear of vehicle')

                self.logger.info(f"🏷️  Plate {i}: {plate_number} | {clean_location} | Confidence: {confidence}")

                # Log to license plate specific logger
                self.license_plate_logger.info(
                    f"DETECTED: {plate_number} | {clean_location} | {confidence} | {os.path.basename(ai_result['image_path'])}"
                )

                # Print to console for immediate visibility
                print(f"   🏷️  {plate_number} ({confidence} confidence)")
        else:
            self.logger.info("ℹ️  No license plates detected")
            print("   ℹ️  No plates detected")

    def _handle_object_detection_result(self, ai_result: dict):
        """Handle object detection results"""
        description = ai_result.get('description', 'No description available')
        word_count = ai_result.get('word_count', 0)

        self.logger.info(f"👁️  Scene: {description} ({word_count} words)")

        # Log to license plate logger for now (could create separate object logger)
        self.license_plate_logger.info(
            f"SCENE: {description} | {word_count} words | {os.path.basename(ai_result['image_path'])}"
        )

        # Print to console for immediate visibility
        print(f"   👁️  {description}")
        if word_count > self.mode_config.get('max_words', 15):
            print(f"   ⚠️  Response was truncated (original: {word_count} words)")

    def start(self):
        """Start the monitoring system"""
        mode_name = self.mode_config.get('name', self.current_mode)
        print(f"\n🚀 Starting AI Vision Monitoring System ({mode_name})...")
        print("=" * 50)

        # Test AI processor first
        self.logger.info("🤖 Testing AI processor connection...")
        if not self.ai_processor.test_api_connection():
            self.logger.warning(f"AI processor test failed. {mode_name} may not work.")
            print("⚠️  AI processing may be limited without valid API key")

        # Test camera connection with proper fallback
        self.logger.info("📷 Testing camera connection...")

        # If we're using real camera, try to connect with retries
        if not self.use_mock_camera and isinstance(self.camera_handler, RTSPCameraHandler):
            self.logger.info("🔄 Attempting to connect to real RTSP camera...")
            if self.camera_handler.connect():
                self.logger.info("✅ Real RTSP camera connected successfully!")
                print("📹 Connected to real RTSP camera")
            else:
                self.logger.warning("❌ Real camera connection failed after all attempts")
                self.logger.info("🎭 Switching to mock camera for demonstration")
                print("🎭 Falling back to mock camera")
                self.camera_handler = MockCameraHandler()
                self.use_mock_camera = True

                if not self.camera_handler.connect():
                    self.logger.error("❌ Even mock camera failed. System cannot start.")
                    return False
        else:
            # Using mock camera
            if not self.camera_handler.connect():
                self.logger.error("❌ Camera connection failed. System cannot start.")
                return False

        # Setup scheduling
        capture_config = self.config_manager.get_config().get('capture', {})
        storage_config = self.config_manager.get_storage_config()
        interval_minutes = capture_config.get('interval_minutes', 1)
        images_dir = storage_config.get('images_directory', 'captured_images')

        self.logger.info(f"⏰ Scheduling capture every {interval_minutes} minute(s)")

        schedule.every(interval_minutes).minutes.do(self.capture_and_process)

        self.running = True
        print(f"✅ System started successfully!")
        print(f"⏰ Monitoring every {interval_minutes} minute(s)")
        print(f"📁 Images saved to: {images_dir}/")
        print(f"📝 Logs saved to: logs/")
        print(f"🎯 Mode: {mode_name}")
        print("\n🔄 Monitoring active... (Press Ctrl+C to stop)")
        print("-" * 50)

        try:
            # Run the scheduler
            while self.running:
                schedule.run_pending()
                time.sleep(1)

        except KeyboardInterrupt:
            print("\n\n🛑 Stopping system...")
            self.logger.info("Received keyboard interrupt")
        finally:
            self.stop()

        return True

    def stop(self):
        """Stop the monitoring system"""
        self.logger.info("🛑 Stopping License Plate Monitoring System...")
        self.running = False

        # Disconnect camera
        self.camera_handler.disconnect()

        # Clear scheduled jobs
        schedule.clear()

        print("✅ System stopped successfully")
        self.logger.info("✅ License Plate Monitoring System stopped")

    def run_single_capture(self):
        """Run a single capture for testing purposes"""
        self.logger.info("Running single capture test...")

        if self.camera_handler.connect():
            result = self.camera_handler.capture_image()
            if result:
                image_path, frame = result
                self.logger.info(f"Single capture successful: {image_path}")
                return image_path
            else:
                self.logger.error("Single capture failed")
                return None
        else:
            self.logger.error("Camera connection failed")
            return None


def main():
    """Main entry point"""
    # Load config to get current mode info
    config_mgr = get_config_manager()
    current_mode = config_mgr.get_current_mode()
    mode_config = config_mgr.get_mode_config()
    mode_name = mode_config.get('name', current_mode)

    parser = argparse.ArgumentParser(description=f'AI Vision Monitoring System - {mode_name}')
    parser.add_argument('--test', action='store_true', help='Run a single capture test')
    parser.add_argument('--mock', action='store_true', help='Use mock camera instead of real RTSP camera')
    parser.add_argument('--ai-test', action='store_true', help='Test AI processor only')
    parser.add_argument('--mode', choices=['license_plate', 'object_detection'], help='Set operating mode')
    args = parser.parse_args()

    # Set mode if specified
    if args.mode:
        config_mgr.set_mode(args.mode)
        current_mode = args.mode
        mode_config = config_mgr.get_mode_config()
        mode_name = mode_config.get('name', current_mode)

    print(f"\n🤖 AI Vision Monitoring System - {mode_name}")
    print("=" * 50)

    # AI processor test
    if args.ai_test:
        print("Testing AI Processor...")
        processor = AIVisionProcessor()
        if processor.test_api_connection():
            print("✓ AI Processor connection successful!")
            sys.exit(0)
        else:
            print("✗ AI Processor connection failed!")
            sys.exit(1)

    # Single capture test
    if args.test:
        print(f"Running single capture and AI processing test ({mode_name})...")
        monitor = AIVisionMonitor(use_mock_camera=args.mock)

        # Test camera
        if monitor.camera_handler.connect():
            result = monitor.camera_handler.capture_image()
            if result:
                image_path, frame = result
                print(f"✓ Image captured: {image_path}")

                # Test AI processing
                print(f"Processing with AI ({mode_name})...")
                ai_result = monitor.ai_processor.process_image(image_path, current_mode)

                if ai_result['success']:
                    if current_mode == "license_plate":
                        plates_detected = ai_result.get('plates_detected', 0)
                        print(f"✓ AI processing completed. License plates detected: {plates_detected}")

                        if plates_detected > 0:
                            for i, plate in enumerate(ai_result['license_plates'], 1):
                                plate_number = plate.get('plate_number', 'Unknown')
                                location = plate.get('location', 'Unknown location')
                                confidence = plate.get('confidence', 'Unknown')
                                print(f"  Plate {i}: {plate_number} (Location: {location}, Confidence: {confidence})")
                    elif current_mode == "object_detection":
                        description = ai_result.get('description', 'No description')
                        word_count = ai_result.get('word_count', 0)
                        print(f"✓ AI processing completed. Scene description: {description}")
                        print(f"  Word count: {word_count}")

                    print("Test successful!")
                    sys.exit(0)
                else:
                    error_msg = ai_result.get('error', 'Unknown error')
                    print(f"✗ AI processing failed: {error_msg}")
                    sys.exit(1)
            else:
                print("✗ Image capture failed!")
                sys.exit(1)
        else:
            print("✗ Camera connection failed!")
            sys.exit(1)

    # Normal operation
    print(f"Starting continuous monitoring ({mode_name})...")
    monitor = AIVisionMonitor(use_mock_camera=args.mock)

    try:
        success = monitor.start()
        if not success:
            sys.exit(1)
    except Exception as e:
        monitor.logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()