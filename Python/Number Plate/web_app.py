#!/usr/bin/env python3
"""
Web Application for License Plate Recognition System
Provides web interface for manual capture and auto mode control
"""

import os
import json
import threading
import time
import webbrowser
from datetime import datetime
from typing import Optional, Dict, Any
from flask import Flask, render_template, jsonify, request, send_file
from flask_cors import CORS

# Import existing system components
from main import AIVisionMonitor
from config_manager import get_config_manager
from logger import get_logger

app = Flask(__name__)
CORS(app)

class WebLicensePlateSystem:
    def __init__(self):
        """Initialize the web-based license plate system"""
        self.config_manager = get_config_manager()
        self.logger = get_logger()
        self.monitor = None
        self.auto_mode_thread = None
        self.auto_mode_running = False
        self.current_mode = "web"
        self.last_capture_result = None
        self.system_status = "ready"
        
        # Initialize the monitor
        self._initialize_monitor()
        
    def _initialize_monitor(self):
        """Initialize the AI vision monitor with fallback to mock camera"""
        try:
            # First try with real camera
            self.monitor = AIVisionMonitor(use_mock_camera=False)
            self.logger.info("✅ AI Vision Monitor initialized for web interface")
        except Exception as e:
            self.logger.warning(f"⚠️ Failed to initialize with real camera: {str(e)}")
            try:
                # Fallback to mock camera
                self.logger.info("🔄 Falling back to mock camera...")
                self.monitor = AIVisionMonitor(use_mock_camera=True)
                self.logger.info("✅ AI Vision Monitor initialized with mock camera")
            except Exception as e2:
                self.logger.error(f"❌ Failed to initialize even with mock camera: {str(e2)}")
                self.system_status = "error"
    
    def manual_capture(self) -> Dict[str, Any]:
        """Perform a manual capture and return results"""
        if not self.monitor:
            return {
                'success': False,
                'error': 'System not initialized',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            self.system_status = "capturing"
            self.logger.info("📸 Manual capture initiated from web interface")
            
            # Perform capture and processing
            self.monitor.capture_and_process()
            
            # Get the latest captured image
            latest_image = self._get_latest_image()
            
            result = {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'image_path': latest_image,
                'mode': 'manual'
            }
            
            self.last_capture_result = result
            self.system_status = "ready"
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Manual capture failed: {str(e)}")
            self.system_status = "error"
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def start_auto_mode(self, interval_seconds: int) -> Dict[str, Any]:
        """Start auto mode with specified interval"""
        if self.auto_mode_running:
            self.stop_auto_mode()
        
        try:
            self.current_mode = "auto"
            self.auto_mode_running = True
            
            # Update config with new interval
            config = self.config_manager.get_config()
            config['operation']['mode'] = 'auto'
            config['operation']['auto_mode']['interval_seconds'] = interval_seconds
            config['operation']['auto_mode']['enabled'] = True
            config['operation']['web_mode']['enabled'] = False
            
            # Start auto mode thread
            self.auto_mode_thread = threading.Thread(
                target=self._auto_mode_worker,
                args=(interval_seconds,),
                daemon=True
            )
            self.auto_mode_thread.start()
            
            self.logger.info(f"🔄 Auto mode started with {interval_seconds}s interval")
            
            return {
                'success': True,
                'mode': 'auto',
                'interval_seconds': interval_seconds,
                'message': f'Auto mode started with {interval_seconds} second interval'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start auto mode: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def stop_auto_mode(self) -> Dict[str, Any]:
        """Stop auto mode and return to web mode"""
        try:
            self.auto_mode_running = False
            self.current_mode = "web"
            
            # Update config
            config = self.config_manager.get_config()
            config['operation']['mode'] = 'web'
            config['operation']['auto_mode']['enabled'] = False
            config['operation']['web_mode']['enabled'] = True
            
            if self.auto_mode_thread and self.auto_mode_thread.is_alive():
                # Wait for thread to finish
                self.auto_mode_thread.join(timeout=2)
            
            self.logger.info("🔄 Auto mode stopped, switched to web mode")
            
            return {
                'success': True,
                'mode': 'web',
                'message': 'Switched to web mode'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to stop auto mode: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _auto_mode_worker(self, interval_seconds: int):
        """Worker thread for auto mode captures"""
        self.logger.info(f"🤖 Auto mode worker started (interval: {interval_seconds}s)")
        
        while self.auto_mode_running:
            try:
                self.system_status = "auto_capturing"
                self.monitor.capture_and_process()
                
                # Update last capture result
                latest_image = self._get_latest_image()
                self.last_capture_result = {
                    'success': True,
                    'timestamp': datetime.now().isoformat(),
                    'image_path': latest_image,
                    'mode': 'auto'
                }
                
                self.system_status = "auto_waiting"
                
                # Wait for the specified interval
                for _ in range(interval_seconds):
                    if not self.auto_mode_running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"❌ Auto mode capture failed: {str(e)}")
                self.system_status = "error"
                time.sleep(5)  # Wait before retrying
        
        self.logger.info("🔄 Auto mode worker stopped")
        self.system_status = "ready"
    
    def _get_latest_image(self) -> Optional[str]:
        """Get the path to the latest captured image"""
        try:
            storage_config = self.config_manager.get_storage_config()
            images_dir = storage_config.get('images_directory', 'captured_images')
            
            if not os.path.exists(images_dir):
                return None
            
            # Get all image files
            image_files = [f for f in os.listdir(images_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            if not image_files:
                return None
            
            # Sort by modification time and get the latest
            image_files.sort(key=lambda x: os.path.getmtime(os.path.join(images_dir, x)), reverse=True)
            latest_image = os.path.join(images_dir, image_files[0])
            
            return latest_image
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get latest image: {str(e)}")
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            'mode': self.current_mode,
            'status': self.system_status,
            'auto_mode_running': self.auto_mode_running,
            'last_capture': self.last_capture_result,
            'timestamp': datetime.now().isoformat()
        }

# Initialize the system
web_system = WebLicensePlateSystem()

# Flask Routes
@app.route('/')
def index():
    """Main web interface"""
    return render_template('index.html')

@app.route('/api/capture', methods=['POST'])
def api_capture():
    """Manual capture endpoint"""
    result = web_system.manual_capture()
    return jsonify(result)

@app.route('/api/start_auto', methods=['POST'])
def api_start_auto():
    """Start auto mode endpoint"""
    data = request.get_json()
    interval_seconds = data.get('interval_seconds', 60)
    
    # Validate interval
    if not isinstance(interval_seconds, int) or interval_seconds < 1:
        return jsonify({
            'success': False,
            'error': 'Invalid interval. Must be a positive integer.'
        }), 400
    
    result = web_system.start_auto_mode(interval_seconds)
    return jsonify(result)

@app.route('/api/stop_auto', methods=['POST'])
def api_stop_auto():
    """Stop auto mode endpoint"""
    result = web_system.stop_auto_mode()
    return jsonify(result)

@app.route('/api/status', methods=['GET'])
def api_status():
    """Get system status endpoint"""
    status = web_system.get_status()
    return jsonify(status)

@app.route('/api/image/<path:filename>')
def api_image(filename):
    """Serve captured images"""
    try:
        storage_config = web_system.config_manager.get_storage_config()
        images_dir = storage_config.get('images_directory', 'captured_images')
        image_path = os.path.join(images_dir, filename)
        
        if os.path.exists(image_path):
            return send_file(image_path)
        else:
            return jsonify({'error': 'Image not found'}), 404
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/latest_image')
def api_latest_image():
    """Get the latest captured image"""
    try:
        latest_image = web_system._get_latest_image()
        if latest_image:
            return send_file(latest_image)
        else:
            return jsonify({'error': 'No images found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def run_web_server():
    """Run the Flask web server"""
    config = get_config_manager().get_config()
    web_config = config.get('web_server', {})
    
    host = web_config.get('host', '0.0.0.0')
    port = web_config.get('port', 8080)
    debug = web_config.get('debug', False)
    auto_open = web_config.get('auto_open_browser', True)
    
    # Open browser automatically
    if auto_open:
        threading.Timer(1.5, lambda: webbrowser.open(f'http://localhost:{port}')).start()
    
    print(f"🌐 Starting License Plate Recognition Web Interface...")
    print(f"🔗 Access at: http://localhost:{port}")
    print(f"📱 Or from other devices: http://{host}:{port}")
    print(f"🔄 Mode: Web Interface with Auto Mode Support")
    
    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    run_web_server()
